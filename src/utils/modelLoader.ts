import * as THREE from 'three';
import { GLTF } from 'three-stdlib';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';

// Animation state mapping
export type AnimationState = 
  | 'idle' 
  | 'hello' 
  | 'happy' 
  | 'clapping' 
  | 'curious' 
  | 'dash' 
  | 'frustrated' 
  | 'again'
  | 'speaking'
  | 'listening';

// Model loading configuration
export interface ModelConfig {
  modelPath: string;
  texturePath?: string;
  animationsPath?: string;
  scale?: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
}

// Default model configuration for LABot
export const DEFAULT_MODEL_CONFIG: ModelConfig = {
  modelPath: '/LABot/LABot.fbx',
  texturePath: '/LABot/Texture/',
  animationsPath: '/LABot/',
  scale: 0.01, // FBX models are often larger, scale down
  position: [0, -1, 0],
  rotation: [0, 0, 0],
};

// LABot specific animation file mapping - complete animated models
export const LABOT_ANIMATION_FILES: Record<AnimationState, string> = {
  idle: '/LABot/<EMAIL>',
  hello: '/LABot/<EMAIL>',
  happy: '/LABot/<EMAIL>',
  clapping: '/LABot/<EMAIL>',
  curious: '/LABot/<EMAIL>',
  dash: '/LABot/<EMAIL>',
  frustrated: '/LABot/<EMAIL>',
  again: '/LABot/<EMAIL>',
  speaking: '/LABot/<EMAIL>', // Use hello for speaking
  listening: '/LABot/<EMAIL>', // Use curious for listening
};

// Function to get available animation files dynamically
export async function getAvailableAnimations(): Promise<string[]> {
  const availableAnimations: string[] = [];

  for (const [animationName, filePath] of Object.entries(LABOT_ANIMATION_FILES)) {
    const exists = await checkFileExists(filePath);
    if (exists) {
      availableAnimations.push(animationName);
    }
  }

  return availableAnimations;
}

// Function to load a complete animated model
export async function loadAnimatedModel(animationName: string, config: ModelConfig): Promise<{
  model: THREE.Group;
  animations: THREE.AnimationClip[];
} | null> {
  const filePath = LABOT_ANIMATION_FILES[animationName as AnimationState];
  if (!filePath) {
    console.warn(`Animation ${animationName} not found in LABOT_ANIMATION_FILES`);
    return null;
  }

  try {
    const exists = await checkFileExists(filePath);
    if (!exists) {
      console.warn(`Animation file does not exist: ${filePath}`);
      return null;
    }

    console.log(`Loading complete animated model: ${animationName} from ${filePath}`);
    const fbxModel = await loadFBXModel(filePath);

    console.log('🎨 Will apply base color texture during model preparation');

    // Check for bones/skeleton
    let hasBones = false;
    let boneCount = 0;
    fbxModel.traverse((child) => {
      if (child.type === 'Bone') {
        hasBones = true;
        boneCount++;
      }
    });

    // Analyze model structure for debugging
    const meshes: Array<{name: string, materialName: string, materialType: string, hasUV: boolean, isEyeMesh: boolean}> = [];
    fbxModel.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        const meshName = child.name.toLowerCase();
        const isEyeMesh = meshName.includes('eye') ||
                         meshName.includes('pupil') ||
                         meshName.includes('iris') ||
                         meshName.includes('eyeball');

        meshes.push({
          name: child.name,
          materialName: (child.material as THREE.Material)?.name || 'unnamed',
          materialType: child.material?.constructor.name,
          hasUV: !!(child.geometry as THREE.BufferGeometry)?.attributes?.uv,
          isEyeMesh: isEyeMesh
        });
      }
    });

    console.log(`🎬 FBX Model loaded for ${animationName}:`, {
      animations: fbxModel.animations,
      animationCount: fbxModel.animations?.length || 0,
      animationNames: fbxModel.animations?.map(clip => clip.name) || [],
      hasAnimations: !!(fbxModel.animations && fbxModel.animations.length > 0),
      hasBones,
      boneCount,
      willApplyBaseTexture: true,
      meshCount: meshes.length,
      meshes: meshes,
      children: fbxModel.children.map(child => ({ name: child.name, type: child.type }))
    });

    const preparedModel = prepareFBXModel(fbxModel, config);

    return {
      model: preparedModel,
      animations: fbxModel.animations || []
    };
  } catch (error) {
    console.error(`Failed to load animated model ${animationName}:`, error);
    return null;
  }
}

// Texture types that can be loaded
export interface TextureSet {
  diffuse?: THREE.Texture;
  normal?: THREE.Texture;
  roughness?: THREE.Texture;
  metallic?: THREE.Texture;
  emissive?: THREE.Texture;
  ao?: THREE.Texture;
  eyeDiffuse?: THREE.Texture;
}

// Animation mapping for different contexts
export const ANIMATION_MAPPING: Record<string, AnimationState[]> = {
  idle: ['idle'],
  speaking: ['hello', 'happy', 'clapping'],
  listening: ['curious'],
  positive: ['happy', 'clapping', 'hello'],
  negative: ['frustrated'],
  action: ['dash', 'again'],
};

/**
 * Check if a file exists by attempting to fetch it
 */
export async function checkFileExists(url: string): Promise<boolean> {
  try {
    console.log('Checking file exists:', url);
    const response = await fetch(url, { method: 'HEAD' });
    console.log('File check response:', url, response.status, response.ok);
    return response.ok;
  } catch (error) {
    console.warn(`File check failed for ${url}:`, error);
    return false;
  }
}

/**
 * Discover available animation files dynamically from the LABot directory
 */
export async function discoverAvailableAnimations(): Promise<Record<string, string>> {
  const discoveredAnimations: Record<string, string> = {};

  // List of potential animation names based on file naming convention
  const potentialAnimations = [
    'Idle', 'Hello', 'Happy', 'Clapping', 'Curious',
    'Dash', 'Frustrated', 'Again'
  ];

  for (const animName of potentialAnimations) {
    const filePath = `/LABot/LABot@${animName}.fbx`;
    const exists = await checkFileExists(filePath);
    if (exists) {
      // Convert to lowercase for consistency with our animation state types
      const normalizedName = animName.toLowerCase();
      discoveredAnimations[normalizedName] = filePath;
      console.log(`Discovered animation: ${normalizedName} -> ${filePath}`);
    }
  }

  return discoveredAnimations;
}

/**
 * Load textures from the texture directory
 */
export async function loadTextures(texturePath: string): Promise<TextureSet> {
  const textureLoader = new THREE.TextureLoader();
  const textures: TextureSet = {};

  // Load all available LABot textures
  const textureFiles = [
    { key: 'diffuse', files: ['LABot_Base_Color.jpg'] },
    { key: 'normal', files: ['LABot_Normal.png'] },
    { key: 'roughness', files: ['LABot_Roughness.jpg'] },
    { key: 'metallic', files: ['LABot_Metallic.jpg'] },
    { key: 'ao', files: ['LABot_Mixed_AO.jpg'] },
    { key: 'emissive', files: ['LABot_Height.jpg'] }, // Use height as emissive for now
    { key: 'eyeDiffuse', files: ['eye_Base_Color.png'] }, // Eye texture
  ];

  console.log('🎨 Loading textures from:', texturePath);

  for (const { key, files } of textureFiles) {
    for (const file of files) {
      const url = `${texturePath}${file}`;
      if (await checkFileExists(url)) {
        try {
          const texture = textureLoader.load(url);
          configureTexture(texture);
          textures[key as keyof TextureSet] = texture;
          console.log(`✅ Loaded texture: ${key} from ${file}`);
          break; // Found and loaded, move to next texture type
        } catch (error) {
          console.warn(`Failed to load texture: ${url}`, error);
        }
      } else {
        console.warn(`❌ Texture file not found: ${url}`);
      }
    }
  }

  console.log('🎨 Loaded textures:', Object.keys(textures));
  return textures;
}

/**
 * Configure texture properties for optimal rendering
 */
export function configureTexture(texture: THREE.Texture): void {
  texture.wrapS = THREE.RepeatWrapping;
  texture.wrapT = THREE.RepeatWrapping;
  texture.generateMipmaps = true;
  texture.minFilter = THREE.LinearMipmapLinearFilter;
  texture.magFilter = THREE.LinearFilter;
  texture.flipY = false; // FBX textures typically don't need flipping
  texture.colorSpace = THREE.SRGBColorSpace; // Ensure proper color space
}

/**
 * Apply textures to a material with enhanced configuration
 */
export function applyTexturesToMaterial(
  material: THREE.Material,
  textures: TextureSet
): void {
  if (material instanceof THREE.MeshStandardMaterial ||
      material instanceof THREE.MeshPhysicalMaterial) {

    if (textures.diffuse) {
      configureTexture(textures.diffuse);
      material.map = textures.diffuse;
      material.needsUpdate = true;
    }

    if (textures.normal) {
      configureTexture(textures.normal);
      material.normalMap = textures.normal;
      material.normalScale = new THREE.Vector2(1, 1);
      material.needsUpdate = true;
    }

    if (textures.roughness) {
      configureTexture(textures.roughness);
      material.roughnessMap = textures.roughness;
      material.roughness = 1.0; // Let the map control roughness
      material.needsUpdate = true;
    }

    if (textures.metallic) {
      configureTexture(textures.metallic);
      material.metalnessMap = textures.metallic;
      material.metalness = 1.0; // Let the map control metalness
      material.needsUpdate = true;
    }

    if (textures.emissive) {
      configureTexture(textures.emissive);
      material.emissiveMap = textures.emissive;
      material.emissiveIntensity = 1.0;
      material.needsUpdate = true;
    }

    if (textures.ao) {
      configureTexture(textures.ao);
      material.aoMap = textures.ao;
      material.aoMapIntensity = 1.0;
      material.needsUpdate = true;
    }

    // Enable environment mapping for better reflections
    if (material instanceof THREE.MeshStandardMaterial) {
      material.envMapIntensity = 1.0;
    }
  }
}

/**
 * Material mapping for different parts of the character
 */
export interface MaterialMapping {
  [materialName: string]: {
    textures: Partial<TextureSet>;
    properties?: {
      roughness?: number;
      metalness?: number;
      emissiveIntensity?: number;
      normalScale?: number;
    };
  };
}

/**
 * Apply textures to specific materials based on mapping
 */
export function applyTexturesWithMapping(
  gltf: GLTF,
  textures: TextureSet,
  mapping?: MaterialMapping
): void {
  gltf.scene.traverse((child) => {
    if (child instanceof THREE.Mesh && child.material) {
      const materials = Array.isArray(child.material) ? child.material : [child.material];

      materials.forEach(material => {
        // Check if we have specific mapping for this material
        const materialMapping = mapping?.[material.name];

        if (materialMapping) {
          // Apply specific textures for this material
          applyTexturesToMaterial(material, materialMapping.textures);

          // Apply material properties
          if (materialMapping.properties &&
              (material instanceof THREE.MeshStandardMaterial ||
               material instanceof THREE.MeshPhysicalMaterial)) {

            const props = materialMapping.properties;
            if (props.roughness !== undefined) material.roughness = props.roughness;
            if (props.metalness !== undefined) material.metalness = props.metalness;
            if (props.emissiveIntensity !== undefined) material.emissiveIntensity = props.emissiveIntensity;
            if (props.normalScale !== undefined && material.normalMap) {
              material.normalScale = new THREE.Vector2(props.normalScale, props.normalScale);
            }
          }
        } else {
          // Apply all textures to materials without specific mapping
          applyTexturesToMaterial(material, textures);
        }
      });
    }
  });
}

/**
 * Apply textures to all materials in a GLTF scene (legacy function)
 */
export function applyTexturesToGLTF(gltf: GLTF, textures: TextureSet): void {
  applyTexturesWithMapping(gltf, textures);
}

/**
 * Get the best animation for a given context
 */
export function getAnimationForContext(
  context: string, 
  availableAnimations: string[]
): string | null {
  const contextAnimations = ANIMATION_MAPPING[context] || [context];
  
  for (const animName of contextAnimations) {
    if (availableAnimations.includes(animName)) {
      return animName;
    }
  }
  
  // Fallback to idle if available
  if (availableAnimations.includes('idle')) {
    return 'idle';
  }
  
  // Return first available animation
  return availableAnimations[0] || null;
}

/**
 * Load FBX model
 */
export async function loadFBXModel(modelPath: string): Promise<THREE.Group> {
  console.log('Starting FBX model load:', modelPath);
  const loader = new FBXLoader();

  return new Promise((resolve, reject) => {
    loader.load(
      modelPath,
      (fbx) => {
        console.log('FBX model loaded successfully:', modelPath, fbx);
        resolve(fbx);
      },
      (progress) => {
        console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
      },
      (error) => {
        console.error('FBX loading error:', modelPath, error);
        reject(error);
      }
    );
  });
}

/**
 * Load FBX animation
 */
export async function loadFBXAnimation(animationPath: string): Promise<THREE.AnimationClip[]> {
  const loader = new FBXLoader();

  return new Promise((resolve, reject) => {
    loader.load(
      animationPath,
      (fbx) => {
        resolve(fbx.animations);
      },
      undefined,
      (error) => {
        reject(error);
      }
    );
  });
}

/**
 * Validate and prepare GLTF model
 */
export function prepareGLTFModel(gltf: GLTF, config: ModelConfig): THREE.Group {
  const model = gltf.scene.clone();

  // Apply transformations
  if (config.scale) {
    model.scale.setScalar(config.scale);
  }

  if (config.position) {
    model.position.set(...config.position);
  }

  if (config.rotation) {
    model.rotation.set(...config.rotation);
  }

  // Ensure all meshes have proper materials
  model.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      if (!child.material) {
        child.material = new THREE.MeshStandardMaterial({ color: 0x888888 });
      }

      // Enable shadows
      child.castShadow = true;
      child.receiveShadow = true;
    }
  });

  return model;
}

/**
 * Apply textures to FBX model (base color + eyes)
 */
export function applyBaseColorToFBX(model: THREE.Group): void {
  console.log('🎨 Applying textures to FBX model');

  // Load textures
  const textureLoader = new THREE.TextureLoader();

  // Load base color texture
  const baseColorTexture = textureLoader.load('/LABot/Texture/LABot_Metallic.jpg',
    (texture) => {
      console.log('✅ Base color texture loaded successfully');
      texture.flipY = false;
      texture.colorSpace = THREE.SRGBColorSpace;
    },
    undefined,
    (error) => {
      console.error('❌ Failed to load base color texture:', error);
    }
  );

  console.log('🎨 Will use procedural colors for different parts');

  model.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      const meshName = child.name.toLowerCase();
      const originalMaterial = child.material as THREE.Material;

      console.log(`🎨 Processing mesh: ${child.name}`, {
        originalMaterialName: originalMaterial?.name,
        originalMaterialType: originalMaterial?.constructor.name,
        originalColor: (originalMaterial as THREE.MeshStandardMaterial)?.color?.getHex?.()
      });

      // Check if this is an eye-related mesh
      const isEyeMesh = meshName.includes('eye') ||
                       meshName.includes('pupil') ||
                       meshName.includes('iris') ||
                       meshName.includes('eyeball') ||
                       meshName.includes('screen'); // Robot screen/visor

      let material: THREE.MeshStandardMaterial;

      if (isEyeMesh) {
        // For eye/screen meshes, use a glowing cyan color
        material = new THREE.MeshStandardMaterial({
          color: 0x00ffff, // Cyan color for eyes/screen
          emissive: 0x004444, // Slight glow
          transparent: true,
          opacity: 0.9,
          roughness: 0.1,
          metalness: 0.0,
          name: `LABot_Eye_Material_${child.name}`
        });
        console.log(`👁️ Applied cyan eye material to: ${child.name}`);
      } else {
        // For body parts, preserve original colors or use smart color assignment
        let bodyColor = 0x888888; // Default gray

        // Try to preserve original material color
        if (originalMaterial && (originalMaterial as THREE.MeshStandardMaterial).color) {
          bodyColor = (originalMaterial as THREE.MeshStandardMaterial).color.getHex();
        } else {
          // Smart color assignment based on mesh name
          if (meshName.includes('orange') || meshName.includes('accent')) {
            bodyColor = 0xff6600; // Orange
          } else if (meshName.includes('head') || meshName.includes('helmet')) {
            bodyColor = 0xcccccc; // Light gray
          } else if (meshName.includes('body') || meshName.includes('torso')) {
            bodyColor = 0x999999; // Medium gray
          }
        }

        material = new THREE.MeshStandardMaterial({
          color: bodyColor,
          map: baseColorTexture, // Apply texture for detail
          roughness: 0.7,
          metalness: 0.1,
          name: `LABot_Body_Material_${child.name}`
        });
        console.log(`🎨 Applied body material to: ${child.name} with color: #${bodyColor.toString(16)}`);
      }

      // Apply the material
      child.material = material;
      child.castShadow = true;
      child.receiveShadow = true;
    }
  });
}

/**
 * Validate and prepare FBX model
 */
export function prepareFBXModel(fbx: THREE.Group, config: ModelConfig): THREE.Group {
  const model = fbx.clone();

  // Apply transformations
  if (config.scale) {
    model.scale.setScalar(config.scale);
  }

  if (config.position) {
    model.position.set(...config.position);
  }

  if (config.rotation) {
    model.rotation.set(...config.rotation);
  }

  // Always apply the base color texture
  applyBaseColorToFBX(model);

  return model;
}
