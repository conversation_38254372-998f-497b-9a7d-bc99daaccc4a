import { checkFileExists } from './modelLoader';

/**
 * Validate LABot file structure and accessibility
 */
export async function validateLABotSetup(): Promise<{
  mainModel: boolean;
  animations: Record<string, boolean>;
  textures: Record<string, boolean>;
  summary: string;
  recommendations: string[];
}> {
  const results = {
    mainModel: false,
    animations: {} as Record<string, boolean>,
    textures: {} as Record<string, boolean>,
    summary: '',
    recommendations: [] as string[]
  };

  // Check main model
  try {
    results.mainModel = await checkFileExists('/LABot/LABot.fbx');
    if (!results.mainModel) {
      results.recommendations.push('Main model LABot.fbx not found or not accessible');
    }
  } catch (error) {
    results.recommendations.push('Error checking main model: ' + (error as Error).message);
  }

  // Check animations
  const animationFiles = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  for (const animFile of animationFiles) {
    try {
      results.animations[animFile] = await checkFileExists(`/LABot/${animFile}`);
      if (!results.animations[animFile]) {
        results.recommendations.push(`Animation file ${animFile} not found`);
      }
    } catch (error) {
      results.animations[animFile] = false;
      results.recommendations.push(`Error checking ${animFile}: ` + (error as Error).message);
    }
  }

  // Check textures
  const textureFiles = [
    'LABot_Base_Color.jpg',
    'LABot_Normal.png',
    'LABot_Roughness.jpg',
    'LABot_Metallic.jpg',
    'LABot_Mixed_AO.jpg',
    'LABot_Height.jpg',
    'eye_Base_Color.png'
  ];

  for (const textureFile of textureFiles) {
    try {
      results.textures[textureFile] = await checkFileExists(`/LABot/Texture/${textureFile}`);
      if (!results.textures[textureFile]) {
        results.recommendations.push(`Texture file ${textureFile} not found`);
      }
    } catch (error) {
      results.textures[textureFile] = false;
      results.recommendations.push(`Error checking ${textureFile}: ` + (error as Error).message);
    }
  }

  // Generate summary
  const foundAnimations = Object.values(results.animations).filter(Boolean).length;
  const foundTextures = Object.values(results.textures).filter(Boolean).length;
  
  results.summary = `LABot Setup Status:
- Main Model: ${results.mainModel ? '✅' : '❌'}
- Animations: ${foundAnimations}/${animationFiles.length} found
- Textures: ${foundTextures}/${textureFiles.length} found`;

  if (results.mainModel && foundAnimations > 0) {
    results.recommendations.unshift('✅ LABot setup looks good! Model and animations should load.');
  } else {
    results.recommendations.unshift('⚠️ LABot setup incomplete. Check file accessibility.');
  }

  return results;
}

/**
 * Run validation and log results to console
 */
export async function runLABotValidation(): Promise<void> {
  console.log('🔍 Validating LABot setup...');
  
  try {
    const results = await validateLABotSetup();
    
    console.log('\n' + results.summary);
    
    if (results.recommendations.length > 0) {
      console.log('\n📋 Recommendations:');
      results.recommendations.forEach(rec => console.log('  ' + rec));
    }
    
    console.log('\n📊 Detailed Results:');
    console.log('Main Model:', results.mainModel);
    console.log('Animations:', results.animations);
    console.log('Textures:', results.textures);
    
  } catch (error) {
    console.error('❌ Validation failed:', error);
  }
}

// Auto-run validation in development
if (import.meta.env.DEV) {
  // Run validation after a short delay to allow the app to initialize
  setTimeout(runLABotValidation, 2000);
}
