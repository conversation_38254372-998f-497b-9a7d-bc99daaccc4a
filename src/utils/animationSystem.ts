import * as THREE from 'three';
import { AnimationState } from './modelLoader';

export interface AnimationTransition {
  from: string;
  to: string;
  duration: number;
  easing?: (t: number) => number;
}

export interface AnimationConfig {
  name: string;
  loop: boolean;
  weight: number;
  timeScale: number;
  fadeInDuration: number;
  fadeOutDuration: number;
  priority: number;
}

export class AnimationManager {
  private mixer: THREE.AnimationMixer;
  private actions: Map<string, THREE.AnimationAction> = new Map();
  private configs: Map<string, AnimationConfig> = new Map();
  private currentAction: THREE.AnimationAction | null = null;
  private transitionQueue: AnimationTransition[] = [];
  private isTransitioning = false;

  constructor(mixer: THREE.AnimationMixer) {
    this.mixer = mixer;
    this.setupDefaultConfigs();
  }

  private setupDefaultConfigs() {
    const defaultConfigs: Record<AnimationState, Partial<AnimationConfig>> = {
      idle: { loop: true, weight: 1, timeScale: 1, fadeInDuration: 0.5, fadeOutDuration: 0.5, priority: 1 },
      hello: { loop: false, weight: 1, timeScale: 1, fadeInDuration: 0.3, fadeOutDuration: 0.3, priority: 3 },
      happy: { loop: true, weight: 1, timeScale: 1.2, fadeInDuration: 0.4, fadeOutDuration: 0.4, priority: 2 },
      clapping: { loop: true, weight: 1, timeScale: 1, fadeInDuration: 0.3, fadeOutDuration: 0.3, priority: 3 },
      curious: { loop: true, weight: 1, timeScale: 0.8, fadeInDuration: 0.6, fadeOutDuration: 0.6, priority: 2 },
      dash: { loop: false, weight: 1, timeScale: 1.5, fadeInDuration: 0.2, fadeOutDuration: 0.2, priority: 4 },
      frustrated: { loop: true, weight: 1, timeScale: 1.1, fadeInDuration: 0.5, fadeOutDuration: 0.5, priority: 2 },
      again: { loop: false, weight: 1, timeScale: 1, fadeInDuration: 0.3, fadeOutDuration: 0.3, priority: 3 },
      speaking: { loop: true, weight: 1, timeScale: 1, fadeInDuration: 0.4, fadeOutDuration: 0.4, priority: 2 },
      listening: { loop: true, weight: 1, timeScale: 0.9, fadeInDuration: 0.5, fadeOutDuration: 0.5, priority: 2 },
    };

    Object.entries(defaultConfigs).forEach(([name, config]) => {
      this.configs.set(name, {
        name,
        loop: true,
        weight: 1,
        timeScale: 1,
        fadeInDuration: 0.5,
        fadeOutDuration: 0.5,
        priority: 1,
        ...config,
      });
    });
  }

  addAction(name: string, action: THREE.AnimationAction, config?: Partial<AnimationConfig>) {
    this.actions.set(name, action);
    
    if (config) {
      const existingConfig = this.configs.get(name);
      this.configs.set(name, { ...existingConfig, ...config } as AnimationConfig);
    }

    // Configure the action
    const actionConfig = this.configs.get(name);
    if (actionConfig) {
      action.setLoop(actionConfig.loop ? THREE.LoopRepeat : THREE.LoopOnce, actionConfig.loop ? Infinity : 1);
      action.setEffectiveTimeScale(actionConfig.timeScale);
      action.setEffectiveWeight(0); // Start with 0 weight
    }
  }

  async playAnimation(
    name: string, 
    options: {
      crossFade?: boolean;
      duration?: number;
      interrupt?: boolean;
    } = {}
  ): Promise<void> {
    const action = this.actions.get(name);
    const config = this.configs.get(name);
    
    if (!action || !config) {
      console.warn(`Animation "${name}" not found`);
      return;
    }

    const { crossFade = true, duration, interrupt = false } = options;
    const fadeDuration = duration || config.fadeInDuration;

    // Handle interruption
    if (this.isTransitioning && !interrupt) {
      this.transitionQueue.push({
        from: this.currentAction?.getClip().name || '',
        to: name,
        duration: fadeDuration,
      });
      return;
    }

    this.isTransitioning = true;

    // Stop lower priority animations
    if (this.currentAction) {
      const currentConfig = this.configs.get(this.currentAction.getClip().name);
      if (currentConfig && currentConfig.priority < config.priority) {
        await this.stopAnimation(this.currentAction.getClip().name, config.fadeOutDuration);
      }
    }

    // Reset and prepare the new action
    action.reset();
    action.setEffectiveWeight(0);
    action.play();

    // Crossfade or direct transition
    if (crossFade && this.currentAction && this.currentAction !== action) {
      await this.crossFadeToAction(action, fadeDuration);
    } else {
      await this.fadeInAction(action, fadeDuration);
    }

    this.currentAction = action;
    this.isTransitioning = false;

    // Process queued transitions
    if (this.transitionQueue.length > 0) {
      const nextTransition = this.transitionQueue.shift()!;
      this.playAnimation(nextTransition.to, { duration: nextTransition.duration });
    }
  }

  async stopAnimation(name: string, fadeDuration?: number): Promise<void> {
    const action = this.actions.get(name);
    const config = this.configs.get(name);
    
    if (!action || !config) return;

    const duration = fadeDuration || config.fadeOutDuration;
    await this.fadeOutAction(action, duration);
  }

  stopAllAnimations() {
    this.actions.forEach(action => {
      action.stop();
      action.setEffectiveWeight(0);
    });
    this.currentAction = null;
    this.transitionQueue = [];
    this.isTransitioning = false;
  }

  private async fadeInAction(action: THREE.AnimationAction, duration: number): Promise<void> {
    return new Promise((resolve) => {
      const startWeight = 0;
      const endWeight = 1;
      const startTime = performance.now();

      const animate = () => {
        const elapsed = (performance.now() - startTime) / 1000;
        const progress = Math.min(elapsed / duration, 1);
        const weight = startWeight + (endWeight - startWeight) * this.easeInOut(progress);
        
        action.setEffectiveWeight(weight);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          resolve();
        }
      };

      animate();
    });
  }

  private async fadeOutAction(action: THREE.AnimationAction, duration: number): Promise<void> {
    return new Promise((resolve) => {
      const startWeight = action.getEffectiveWeight();
      const endWeight = 0;
      const startTime = performance.now();

      const animate = () => {
        const elapsed = (performance.now() - startTime) / 1000;
        const progress = Math.min(elapsed / duration, 1);
        const weight = startWeight + (endWeight - startWeight) * this.easeInOut(progress);
        
        action.setEffectiveWeight(weight);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          action.stop();
          resolve();
        }
      };

      animate();
    });
  }

  private async crossFadeToAction(newAction: THREE.AnimationAction, duration: number): Promise<void> {
    const oldAction = this.currentAction;
    if (!oldAction) {
      return this.fadeInAction(newAction, duration);
    }

    return new Promise((resolve) => {
      const startTime = performance.now();
      const oldStartWeight = oldAction.getEffectiveWeight();

      const animate = () => {
        const elapsed = (performance.now() - startTime) / 1000;
        const progress = Math.min(elapsed / duration, 1);
        const easedProgress = this.easeInOut(progress);
        
        // Fade out old action
        oldAction.setEffectiveWeight(oldStartWeight * (1 - easedProgress));
        
        // Fade in new action
        newAction.setEffectiveWeight(easedProgress);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          oldAction.stop();
          resolve();
        }
      };

      animate();
    });
  }

  private easeInOut(t: number): number {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  update(deltaTime: number) {
    this.mixer.update(deltaTime);
  }

  getCurrentAnimation(): string | null {
    return this.currentAction?.getClip().name || null;
  }

  getAvailableAnimations(): string[] {
    return Array.from(this.actions.keys());
  }

  isPlaying(name: string): boolean {
    const action = this.actions.get(name);
    return action ? action.isRunning() : false;
  }
}
