import { checkFileExists, loadTextures, getAnimationForContext } from './modelLoader';

/**
 * Test suite for the character system
 */
export class CharacterSystemTester {
  private results: { [key: string]: boolean } = {};
  private errors: { [key: string]: string } = {};

  async runAllTests(): Promise<{ success: boolean; results: any; errors: any }> {
    console.log('🧪 Starting Character System Tests...');

    // Test file existence checking
    await this.testFileExistence();
    
    // Test texture loading
    await this.testTextureLoading();
    
    // Test animation mapping
    this.testAnimationMapping();
    
    // Test error handling
    await this.testErrorHandling();

    const success = Object.values(this.results).every(result => result);
    
    console.log('✅ Character System Tests Complete');
    console.log('Results:', this.results);
    if (Object.keys(this.errors).length > 0) {
      console.log('Errors:', this.errors);
    }

    return {
      success,
      results: this.results,
      errors: this.errors
    };
  }

  private async testFileExistence() {
    try {
      // Test with non-existent file
      const nonExistent = await checkFileExists('/non-existent-file.gltf');
      this.results.fileExistence_nonExistent = !nonExistent;

      // Test with existing file (favicon should exist)
      const existing = await checkFileExists('/favicon.ico');
      this.results.fileExistence_existing = existing;

      console.log('✓ File existence tests passed');
    } catch (error) {
      this.errors.fileExistence = error instanceof Error ? error.message : 'Unknown error';
      this.results.fileExistence_nonExistent = false;
      this.results.fileExistence_existing = false;
    }
  }

  private async testTextureLoading() {
    try {
      // Test texture loading with non-existent path
      const textures = await loadTextures('/non-existent-path/');
      this.results.textureLoading = Object.keys(textures).length === 0;
      
      console.log('✓ Texture loading tests passed');
    } catch (error) {
      this.errors.textureLoading = error instanceof Error ? error.message : 'Unknown error';
      this.results.textureLoading = false;
    }
  }

  private testAnimationMapping() {
    try {
      const availableAnimations = ['idle', 'hello', 'happy', 'clapping'];
      
      // Test basic mapping
      const idleAnim = getAnimationForContext('idle', availableAnimations);
      this.results.animationMapping_idle = idleAnim === 'idle';

      // Test context mapping
      const speakingAnim = getAnimationForContext('speaking', availableAnimations);
      this.results.animationMapping_speaking = ['hello', 'happy', 'clapping'].includes(speakingAnim || '');

      // Test fallback
      const unknownAnim = getAnimationForContext('unknown', availableAnimations);
      this.results.animationMapping_fallback = unknownAnim === 'idle';

      console.log('✓ Animation mapping tests passed');
    } catch (error) {
      this.errors.animationMapping = error instanceof Error ? error.message : 'Unknown error';
      this.results.animationMapping_idle = false;
      this.results.animationMapping_speaking = false;
      this.results.animationMapping_fallback = false;
    }
  }

  private async testErrorHandling() {
    try {
      // Test with invalid URL
      const invalidFile = await checkFileExists('invalid://url');
      this.results.errorHandling_invalidUrl = !invalidFile;

      console.log('✓ Error handling tests passed');
    } catch (error) {
      // This is expected - the function should handle errors gracefully
      this.results.errorHandling_invalidUrl = true;
    }
  }
}

/**
 * Utility function to run tests and log results
 */
export async function runCharacterSystemTests(): Promise<boolean> {
  const tester = new CharacterSystemTester();
  const { success } = await tester.runAllTests();
  return success;
}

/**
 * Validate the current character system setup
 */
export async function validateCharacterSetup(): Promise<{
  hasModelDirectory: boolean;
  hasTextureDirectory: boolean;
  modelFiles: string[];
  textureFiles: string[];
  recommendations: string[];
}> {
  const recommendations: string[] = [];
  
  // Check for model directory
  const hasModelDirectory = await checkFileExists('/LABot/');
  if (!hasModelDirectory) {
    recommendations.push('Create the /LABot/ directory for your 3D model files');
  }

  // Check for texture directory
  const hasTextureDirectory = await checkFileExists('/LABot/Texture/');
  if (!hasTextureDirectory) {
    recommendations.push('Create the /LABot/Texture/ directory for your texture files');
  }

  // Check for common model files
  const modelFiles: string[] = [];
  const commonModelFiles = ['model.gltf', 'model.glb', 'character.gltf', 'character.glb'];
  
  for (const file of commonModelFiles) {
    if (await checkFileExists(`/LABot/${file}`)) {
      modelFiles.push(file);
    }
  }

  if (modelFiles.length === 0) {
    recommendations.push('Add a 3D model file (model.gltf or model.glb) to the /LABot/ directory');
  }

  // Check for common texture files
  const textureFiles: string[] = [];
  const commonTextureFiles = ['diffuse.jpg', 'diffuse.png', 'normal.jpg', 'normal.png'];
  
  for (const file of commonTextureFiles) {
    if (await checkFileExists(`/LABot/Texture/${file}`)) {
      textureFiles.push(file);
    }
  }

  if (textureFiles.length === 0) {
    recommendations.push('Add texture files to the /LABot/Texture/ directory for enhanced visual quality');
  }

  return {
    hasModelDirectory,
    hasTextureDirectory,
    modelFiles,
    textureFiles,
    recommendations
  };
}
