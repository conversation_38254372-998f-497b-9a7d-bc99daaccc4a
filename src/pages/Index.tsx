
import { useState } from 'react';
import TopicCarousel from '@/components/TopicCarousel';
import ChatInterface from '@/components/ChatInterface';

interface Topic {
  id: string;
  title: string;
  description: string;
  image: string;
}

const Index = () => {
  const [currentView, setCurrentView] = useState<'carousel' | 'chat'>('carousel');
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);

  const handleTopicSelect = (topic: Topic) => {
    setSelectedTopic(topic);
  };

  const handleStartChat = () => {
    setCurrentView('chat');
  };

  const handleBackToCarousel = () => {
    setCurrentView('carousel');
    setSelectedTopic(null);
  };

  return (
    <div className="w-full h-screen overflow-hidden">
      {currentView === 'carousel' ? (
        <TopicCarousel
          onTopicSelect={handleTopicSelect}
          onStartChat={handleStartChat}
        />
      ) : (
        <ChatInterface
          selectedTopic={selectedTopic?.title}
          onBack={handleBackToCarousel}
        />
      )}
    </div>
  );
};

export default Index;
