import { useState, useEffect, useRef } from 'react';
import { useGLTF, useAnimations } from '@react-three/drei';
import * as THREE from 'three';
import { GLTF } from 'three-stdlib';
import {
  ModelConfig,
  DEFAULT_MODEL_CONFIG,
  TextureSet,
  AnimationState,
  LABOT_ANIMATION_FILES,
  checkFileExists,
  loadTextures,
  loadFBXModel,
  loadFBXAnimation,
  applyTexturesToGLTF,
  prepareGLTFModel,
  prepareFBXModel,
  getAnimationForContext,
} from '../utils/modelLoader';

export interface UseCharacterModelReturn {
  model: THREE.Group | null;
  isLoading: boolean;
  error: string | null;
  hasCustomModel: boolean;
  animations: Record<string, THREE.AnimationAction>;
  playAnimation: (animationName: string, loop?: boolean) => void;
  stopAnimation: (animationName: string) => void;
  stopAllAnimations: () => void;
  mixer: THREE.AnimationMixer | null;
}

export function useCharacterModel(
  config: Partial<ModelConfig> = {}
): UseCharacterModelReturn {
  const fullConfig = { ...DEFAULT_MODEL_CONFIG, ...config };
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasCustomModel, setHasCustomModel] = useState(false);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [textures, setTextures] = useState<TextureSet>({});
  
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const animationsRef = useRef<Record<string, THREE.AnimationAction>>({});
  const modelRef = useRef<THREE.Group | null>(null);

  // We'll handle FBX loading manually instead of using useGLTF
  const [fbxModel, setFbxModel] = useState<THREE.Group | null>(null);
  const [fbxAnimations, setFbxAnimations] = useState<Record<string, THREE.AnimationClip>>({});

  useEffect(() => {
    async function loadCharacterModel() {
      setIsLoading(true);
      setError(null);

      try {
        // Debug: Log the full config
        console.log('🔍 Full model config:', fullConfig);

        // Check if custom FBX model exists
        const modelExists = await checkFileExists(fullConfig.modelPath);

        if (modelExists) {
          console.log('✅ FBX model exists, loading:', fullConfig.modelPath);

          // Load the main FBX model
          const fbxModel = await loadFBXModel(fullConfig.modelPath);
          console.log('✅ FBX model loaded successfully:', fbxModel);
          setFbxModel(fbxModel);

          // Load textures if texture path is provided
          let loadedTextures: TextureSet = {};
          if (fullConfig.texturePath) {
            try {
              loadedTextures = await loadTextures(fullConfig.texturePath);
              setTextures(loadedTextures);
              console.log('Loaded textures:', Object.keys(loadedTextures));
            } catch (textureError) {
              console.warn('Failed to load textures:', textureError);
            }
          }

          // Prepare the model
          const preparedModel = prepareFBXModel(fbxModel, fullConfig);
          console.log('✅ FBX model prepared:', preparedModel);

          // Apply textures if loaded
          if (Object.keys(loadedTextures).length > 0) {
            console.log('✅ Applying textures to model:', Object.keys(loadedTextures));
            preparedModel.traverse((child) => {
              if (child instanceof THREE.Mesh && child.material) {
                console.log('🎨 Processing mesh:', child.name, 'material type:', child.material.constructor.name);

                const materials = Array.isArray(child.material) ? child.material : [child.material];

                materials.forEach((material, index) => {
                  console.log(`🎨 Material ${index}:`, material.constructor.name, material);

                  // Convert any material to MeshStandardMaterial for better texture support
                  if (!(material instanceof THREE.MeshStandardMaterial)) {
                    console.log('🔄 Converting material to MeshStandardMaterial');
                    const newMaterial = new THREE.MeshStandardMaterial({
                      color: material.color || new THREE.Color(0xffffff),
                      transparent: material.transparent,
                      opacity: material.opacity,
                    });

                    // Replace the material
                    if (Array.isArray(child.material)) {
                      child.material[index] = newMaterial;
                    } else {
                      child.material = newMaterial;
                    }
                    material = newMaterial;
                  }

                  // Apply textures to MeshStandardMaterial
                  if (material instanceof THREE.MeshStandardMaterial) {
                    if (loadedTextures.diffuse) {
                      material.map = loadedTextures.diffuse;
                      console.log('✅ Applied diffuse texture');
                    }
                    if (loadedTextures.normal) {
                      material.normalMap = loadedTextures.normal;
                      material.normalScale = new THREE.Vector2(1, 1);
                      console.log('✅ Applied normal texture');
                    }
                    if (loadedTextures.roughness) {
                      material.roughnessMap = loadedTextures.roughness;
                      material.roughness = 1.0;
                      console.log('✅ Applied roughness texture');
                    }
                    if (loadedTextures.metallic) {
                      material.metalnessMap = loadedTextures.metallic;
                      material.metalness = 0.0; // Let the map control metalness
                      console.log('✅ Applied metallic texture');
                    }
                    if (loadedTextures.ao) {
                      material.aoMap = loadedTextures.ao;
                      material.aoMapIntensity = 1.0;
                      console.log('✅ Applied AO texture');
                    }

                    // Ensure proper texture configuration
                    [material.map, material.normalMap, material.roughnessMap, material.metalnessMap, material.aoMap]
                      .filter(Boolean)
                      .forEach(texture => {
                        if (texture) {
                          texture.wrapS = THREE.RepeatWrapping;
                          texture.wrapT = THREE.RepeatWrapping;
                          texture.flipY = false; // FBX textures typically don't need flipping
                        }
                      });

                    material.needsUpdate = true;
                  }
                });
              }
            });
          }

          setModel(preparedModel);
          setHasCustomModel(true);
          console.log('✅ Model state updated - hasCustomModel:', true, 'model:', preparedModel);

          // Load animations from separate FBX files
          const loadedAnimations: Record<string, THREE.AnimationClip> = {};
          const animationPromises = Object.entries(LABOT_ANIMATION_FILES).map(async ([animName, filePath]) => {
            try {
              const animExists = await checkFileExists(filePath);
              if (animExists) {
                const animations = await loadFBXAnimation(filePath);
                if (animations.length > 0) {
                  loadedAnimations[animName] = animations[0]; // Take the first animation
                  console.log(`Loaded animation: ${animName}`);
                }
              }
            } catch (animError) {
              console.warn(`Failed to load animation ${animName}:`, animError);
            }
          });

          await Promise.all(animationPromises);
          setFbxAnimations(loadedAnimations);

          // Set up animation mixer
          if (Object.keys(loadedAnimations).length > 0) {
            const mixer = new THREE.AnimationMixer(preparedModel);
            mixerRef.current = mixer;

            // Create actions for each animation
            const actions: Record<string, THREE.AnimationAction> = {};
            Object.entries(loadedAnimations).forEach(([name, clip]) => {
              const action = mixer.clipAction(clip);
              actions[name] = action;
            });

            animationsRef.current = actions;
            console.log('Created animation actions:', Object.keys(actions));
          }

        } else {
          console.log('FBX model not found, using fallback');
          setHasCustomModel(false);
          setModel(null);
        }
      } catch (err) {
        console.error('Error loading FBX model:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while loading FBX model';
        setError(errorMessage);
        setHasCustomModel(false);
        setModel(null);

        // Clear any partial state
        mixerRef.current = null;
        animationsRef.current = {};
      } finally {
        setIsLoading(false);
      }
    }

    // Add a small delay to prevent rapid re-loading
    const timeoutId = setTimeout(loadCharacterModel, 100);
    return () => clearTimeout(timeoutId);
  }, [fullConfig.modelPath, fullConfig.texturePath]);

  // Animation control functions
  const playAnimation = (animationName: string, loop: boolean = true) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.reset();
      action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, loop ? Infinity : 1);
      action.play();
    }
  };

  const stopAnimation = (animationName: string) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.stop();
    }
  };

  const stopAllAnimations = () => {
    Object.values(animationsRef.current).forEach(action => {
      action.stop();
    });
  };

  // Update model ref when model changes
  useEffect(() => {
    modelRef.current = model;
  }, [model]);

  return {
    model,
    isLoading,
    error,
    hasCustomModel,
    animations: animationsRef.current,
    playAnimation,
    stopAnimation,
    stopAllAnimations,
    mixer: mixerRef.current,
  };
}

// Hook for managing animation states based on context
export function useCharacterAnimations(
  characterModel: UseCharacterModelReturn,
  animationType: string = 'idle'
) {
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  const previousAnimationType = useRef<string>(animationType);

  useEffect(() => {
    if (!characterModel.hasCustomModel || !characterModel.animations) {
      return;
    }

    const availableAnimations = Object.keys(characterModel.animations);
    const targetAnimation = getAnimationForContext(animationType, availableAnimations);

    // Only change animation if the type has changed and we have a valid target
    if (targetAnimation && 
        animationType !== previousAnimationType.current && 
        targetAnimation !== currentAnimation) {
      
      // Stop current animation
      if (currentAnimation) {
        characterModel.stopAnimation(currentAnimation);
      }

      // Start new animation
      characterModel.playAnimation(targetAnimation, true);
      setCurrentAnimation(targetAnimation);
      previousAnimationType.current = animationType;
    }
  }, [animationType, characterModel, currentAnimation]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      characterModel.stopAllAnimations();
    };
  }, [characterModel]);

  return {
    currentAnimation,
    availableAnimations: Object.keys(characterModel.animations),
  };
}
