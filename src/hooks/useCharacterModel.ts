import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import * as THREE from 'three';
import {
  ModelConfig,
  DEFAULT_MODEL_CONFIG,
  loadAnimatedModel,
  discoverAvailableAnimations,
  getAnimationForContext,
} from '../utils/modelLoader';

export interface UseCharacterModelReturn {
  model: THREE.Group | null;
  isLoading: boolean;
  error: string | null;
  hasCustomModel: boolean;
  animations: Record<string, THREE.AnimationAction>;
  playAnimation: (animationName: string, loop?: boolean) => void;
  stopAnimation: (animationName: string) => void;
  stopAllAnimations: () => void;
  mixer: THREE.AnimationMixer | null;
  availableAnimations: string[];
  currentAnimation: string | null;
  switchToAnimation: (animationName: string) => Promise<void>;
}

export function useCharacterModel(
  config: Partial<ModelConfig> = {}
): UseCharacterModelReturn {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasCustomModel, setHasCustomModel] = useState(false);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [availableAnimations, setAvailableAnimations] = useState<string[]>([]);
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  const [initialized, setInitialized] = useState(false);

  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const animationsRef = useRef<Record<string, THREE.AnimationAction>>({});
  const modelRef = useRef<THREE.Group | null>(null);
  const currentModelCache = useRef<Record<string, THREE.Group>>({});
  const configRef = useRef<ModelConfig>();

  // Store config in ref to avoid dependency issues
  useEffect(() => {
    const newConfig = { ...DEFAULT_MODEL_CONFIG, ...config };
    configRef.current = newConfig;

    // Only initialize once
    if (!initialized) {
      setInitialized(true);
    }
  }, [config, initialized]);

  useEffect(() => {
    let isMounted = true;

    async function initializeCharacterSystem() {
      if (!isMounted) return;

      console.log('🚀 Starting character system initialization, initialized:', initialized);

      if (!initialized) {
        console.log('⏳ Waiting for initialization...');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('🔍 Initializing character system with config:', configRef.current);

        // Discover available animations dynamically
        const discoveredAnimations = await discoverAvailableAnimations();
        if (!isMounted) return;

        const animationNames = Object.keys(discoveredAnimations);

        console.log('🎭 Discovered animations:', animationNames);
        setAvailableAnimations(animationNames);

        if (animationNames.length > 0) {
          // Load the default animation (clapping if available, otherwise idle, otherwise first available)
          const defaultAnimation = animationNames.includes('clapping') ? 'clapping' :
                                  animationNames.includes('idle') ? 'idle' :
                                  animationNames[0];
          console.log('🎬 Loading default animation:', defaultAnimation);

          // Call switchToAnimationInternal directly without dependency issues
          try {
            console.log(`🎬 Switching to animation: ${defaultAnimation}`);

            // Load the complete animated model
            const animatedModelData = await loadAnimatedModel(defaultAnimation, configRef.current!);
            if (!isMounted) return;

            if (!animatedModelData) {
              throw new Error(`Failed to load animated model: ${defaultAnimation}`);
            }

            const { model: animatedModel, animations } = animatedModelData;

            // Cache the model
            currentModelCache.current[defaultAnimation] = animatedModel;

            // Set up animation mixer if animations are available
            if (animations.length > 0) {
              const mixer = new THREE.AnimationMixer(animatedModel);
              mixerRef.current = mixer;

              // Create actions for each animation
              const actions: Record<string, THREE.AnimationAction> = {};
              animations.forEach((clip, index) => {
                const action = mixer.clipAction(clip);
                actions[`${defaultAnimation}_${index}`] = action;
                // Also add with just the animation name for the primary animation
                if (index === 0) {
                  actions[defaultAnimation] = action;
                }
              });

              animationsRef.current = actions;

              // Auto-play the primary animation
              if (actions[defaultAnimation]) {
                actions[defaultAnimation].reset();
                actions[defaultAnimation].setLoop(THREE.LoopRepeat, Infinity);
                actions[defaultAnimation].play();
              }
            }

            if (!isMounted) return;
            setModel(animatedModel);
            setCurrentAnimation(defaultAnimation);
            setHasCustomModel(true);
            console.log(`✅ Successfully initialized with animation: ${defaultAnimation}`);

          } catch (error) {
            console.error(`❌ Failed to load default animation ${defaultAnimation}:`, error);
            if (!isMounted) return;
            setHasCustomModel(false);
            setModel(null);
          }
        } else {
          console.log('❌ No animation files found, using fallback');
          if (!isMounted) return;
          setHasCustomModel(false);
          setModel(null);
        }
      } catch (err) {
        console.error('Error initializing character system:', err);
        if (!isMounted) return;

        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while initializing character system';
        setError(errorMessage);
        setHasCustomModel(false);
        setModel(null);
        setAvailableAnimations([]);
        setCurrentAnimation(null);

        // Clear any partial state
        mixerRef.current = null;
        animationsRef.current = {};
        currentModelCache.current = {};
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    // Add a small delay to prevent rapid re-loading
    const timeoutId = setTimeout(initializeCharacterSystem, 100);

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [initialized]); // Only run when initialized becomes true

  // Animation control functions
  const playAnimation = (animationName: string, loop: boolean = true) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.reset();
      action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, loop ? Infinity : 1);
      action.play();
    }
  };

  const stopAnimation = (animationName: string) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.stop();
    }
  };

  const stopAllAnimations = () => {
    Object.values(animationsRef.current).forEach(action => {
      action.stop();
    });
  };

  // Public function to switch animations
  const switchToAnimation = async (animationName: string): Promise<void> => {
    if (!availableAnimations.includes(animationName)) {
      console.warn(`Animation ${animationName} not available. Available: ${availableAnimations.join(', ')}`);
      return;
    }

    if (currentAnimation === animationName) {
      console.log(`Already on animation: ${animationName}`);
      return;
    }

    try {
      setIsLoading(true);
      console.log(`🎬 Switching to animation: ${animationName}`);

      // Check if we have this model cached
      if (currentModelCache.current[animationName]) {
        console.log(`📦 Using cached model for: ${animationName}`);
        const cachedModel = currentModelCache.current[animationName];
        setModel(cachedModel);
        setCurrentAnimation(animationName);
        return;
      }

      // Load the complete animated model
      const animatedModelData = await loadAnimatedModel(animationName, configRef.current!);
      if (!animatedModelData) {
        throw new Error(`Failed to load animated model: ${animationName}`);
      }

      const { model: animatedModel, animations } = animatedModelData;

      console.log(`🎬 Loaded model for ${animationName}:`, {
        model: animatedModel,
        animationsCount: animations.length,
        animationNames: animations.map(clip => clip.name),
        animationDurations: animations.map(clip => clip.duration)
      });

      // Cache the model
      currentModelCache.current[animationName] = animatedModel;

      // Set up animation mixer if animations are available
      if (animations.length > 0) {
        const mixer = new THREE.AnimationMixer(animatedModel);
        mixerRef.current = mixer;

        // Create actions for each animation
        const actions: Record<string, THREE.AnimationAction> = {};
        animations.forEach((clip, index) => {
          const action = mixer.clipAction(clip);
          actions[`${animationName}_${index}`] = action;
          // Also add with just the animation name for the primary animation
          if (index === 0) {
            actions[animationName] = action;
          }
        });

        animationsRef.current = actions;

        // Auto-play the primary animation
        if (actions[animationName]) {
          console.log(`🎬 Starting animation: ${animationName}`, {
            action: actions[animationName],
            clip: actions[animationName].getClip(),
            duration: actions[animationName].getClip().duration
          });
          actions[animationName].reset();
          actions[animationName].setLoop(THREE.LoopRepeat, Infinity);
          actions[animationName].play();
          console.log(`✅ Animation ${animationName} started, isRunning:`, actions[animationName].isRunning());
        } else {
          console.warn(`❌ No action found for animation: ${animationName}`);
        }
      }

      setModel(animatedModel);
      setCurrentAnimation(animationName);
      console.log(`✅ Successfully switched to animation: ${animationName}`);

    } catch (error) {
      console.error(`Failed to switch to animation ${animationName}:`, error);
      setError(`Failed to switch to animation: ${animationName}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Update model ref when model changes
  useEffect(() => {
    modelRef.current = model;
  }, [model]);

  return {
    model,
    isLoading,
    error,
    hasCustomModel,
    animations: animationsRef.current,
    playAnimation,
    stopAnimation,
    stopAllAnimations,
    mixer: mixerRef.current,
    availableAnimations,
    currentAnimation,
    switchToAnimation,
  };
}

// Hook for managing animation states based on context
export function useCharacterAnimations(
  characterModel: UseCharacterModelReturn,
  animationType: string = 'idle'
) {
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  const previousAnimationType = useRef<string>(animationType);

  useEffect(() => {
    if (!characterModel.hasCustomModel || !characterModel.animations) {
      return;
    }

    const availableAnimations = Object.keys(characterModel.animations);
    const targetAnimation = getAnimationForContext(animationType, availableAnimations);

    // Only change animation if the type has changed and we have a valid target
    if (targetAnimation && 
        animationType !== previousAnimationType.current && 
        targetAnimation !== currentAnimation) {
      
      // Stop current animation
      if (currentAnimation) {
        characterModel.stopAnimation(currentAnimation);
      }

      // Start new animation
      characterModel.playAnimation(targetAnimation, true);
      setCurrentAnimation(targetAnimation);
      previousAnimationType.current = animationType;
    }
  }, [animationType, characterModel, currentAnimation]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      characterModel.stopAllAnimations();
    };
  }, [characterModel]);

  return {
    currentAnimation,
    availableAnimations: Object.keys(characterModel.animations),
  };
}
