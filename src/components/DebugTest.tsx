import { useEffect, useState } from 'react';

export function DebugTest() {
  const [results, setResults] = useState<string[]>([]);

  useEffect(() => {
    async function testFileAccess() {
      console.log('🧪 Testing file access...');
      const testResults: string[] = [];

      // Test main FBX file
      try {
        const response = await fetch('/LABot/LABot.fbx', { method: 'HEAD' });
        const result = `📁 LABot.fbx: ${response.status} ${response.ok ? '✅' : '❌'}`;
        console.log(result);
        testResults.push(result);
      } catch (error) {
        const result = `❌ LABot.fbx error: ${error}`;
        console.error(result);
        testResults.push(result);
      }

      // Test texture files
      const textureFiles = [
        '/LABot/Texture/LABot_Base_Color.jpg',
        '/LABot/Texture/LABot_Normal.png',
        '/LABot/Texture/LABot_Roughness.jpg',
        '/LABot/Texture/LABot_Metallic.jpg',
        '/LABot/Texture/LABot_Mixed_AO.jpg'
      ];

      for (const file of textureFiles) {
        try {
          const response = await fetch(file, { method: 'HEAD' });
          const result = `📁 ${file}: ${response.status} ${response.ok ? '✅' : '❌'}`;
          console.log(result);
          testResults.push(result);
        } catch (error) {
          const result = `❌ ${file} error: ${error}`;
          console.error(result);
          testResults.push(result);
        }
      }

      // Test animation files
      const animationFiles = [
        '/LABot/<EMAIL>',
        '/LABot/<EMAIL>',
        '/LABot/<EMAIL>'
      ];

      for (const file of animationFiles) {
        try {
          const response = await fetch(file, { method: 'HEAD' });
          const result = `🎬 ${file}: ${response.status} ${response.ok ? '✅' : '❌'}`;
          console.log(result);
          testResults.push(result);
        } catch (error) {
          const result = `❌ ${file} error: ${error}`;
          console.error(result);
          testResults.push(result);
        }
      }

      setResults(testResults);
    }

    testFileAccess();
  }, []);

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      fontSize: '12px',
      maxWidth: '400px',
      zIndex: 1000,
      borderRadius: '5px'
    }}>
      <h3>🧪 File Access Test</h3>
      {results.map((result, index) => (
        <div key={index}>{result}</div>
      ))}
    </div>
  );
}
