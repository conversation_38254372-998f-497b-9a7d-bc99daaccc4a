import React, { useState } from 'react';

interface AnimationControlsProps {
  currentAnimation: string;
  availableAnimations: string[];
  onAnimationChange: (animation: string) => void;
}

const AnimationControls: React.FC<AnimationControlsProps> = ({
  currentAnimation,
  availableAnimations,
  onAnimationChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="absolute bottom-4 right-4 z-10">
      {/* Compact dropdown button */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg text-sm font-medium text-gray-800 hover:bg-white/95 transition-colors flex items-center gap-2"
        >
          <span>🎭</span>
          <span>{currentAnimation.charAt(0).toUpperCase() + currentAnimation.slice(1)}</span>
          <span className={`transition-transform ${isOpen ? 'rotate-180' : ''}`}>▼</span>
        </button>

        {/* Dropdown menu */}
        {isOpen && (
          <div className="absolute bottom-full mb-2 right-0 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 min-w-[160px]">
            <div className="p-2">
              <div className="text-xs font-semibold text-gray-600 mb-2 px-2">Animations</div>
              <div className="space-y-1">
                {availableAnimations.map((animation) => (
                  <button
                    key={animation}
                    onClick={() => {
                      onAnimationChange(animation);
                      setIsOpen(false);
                    }}
                    className={`
                      w-full px-3 py-2 text-sm rounded-md transition-colors text-left
                      ${currentAnimation === animation
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                      }
                    `}
                  >
                    {animation.charAt(0).toUpperCase() + animation.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[-1]"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default AnimationControls;
