import React, { useState } from 'react';

interface AnimationControlsProps {
  currentAnimation: string | null;
  availableAnimations: string[];
  onAnimationChange: (animation: string) => void;
  isLoading?: boolean;
}

const AnimationControls: React.FC<AnimationControlsProps> = ({
  currentAnimation,
  availableAnimations,
  onAnimationChange,
  isLoading = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Don't render if no animations are available
  if (availableAnimations.length === 0) {
    return null;
  }

  const displayName = currentAnimation
    ? currentAnimation.charAt(0).toUpperCase() + currentAnimation.slice(1)
    : 'Loading...';

  return (
    <div className="absolute bottom-4 right-4 z-10">
      {/* Compact dropdown button */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          disabled={isLoading}
          className={`bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg text-sm font-medium text-gray-800 hover:bg-white/95 transition-colors flex items-center gap-2 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
        >
          <span>🎭</span>
          <span>{displayName}</span>
          {isLoading ? (
            <span className="animate-spin">⟳</span>
          ) : (
            <span className={`transition-transform ${isOpen ? 'rotate-180' : ''}`}>▼</span>
          )}
        </button>

        {/* Dropdown menu */}
        {isOpen && !isLoading && (
          <div className="absolute bottom-full mb-2 right-0 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 min-w-[160px]">
            <div className="p-2">
              <div className="text-xs font-semibold text-gray-600 mb-2 px-2">
                Animations ({availableAnimations.length})
              </div>
              <div className="space-y-1">
                {availableAnimations.map((animation) => (
                  <button
                    key={animation}
                    onClick={() => {
                      onAnimationChange(animation);
                      setIsOpen(false);
                    }}
                    disabled={isLoading}
                    className={`
                      w-full px-3 py-2 text-sm rounded-md transition-colors text-left
                      ${currentAnimation === animation
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                      }
                      ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                  >
                    <span className="flex items-center justify-between">
                      <span>{animation.charAt(0).toUpperCase() + animation.slice(1)}</span>
                      {currentAnimation === animation && (
                        <span className="text-xs">✓</span>
                      )}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[-1]"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default AnimationControls;
