
import { useRef, useEffect, useState, useMemo } from 'react';
import { Canvas, useFrame, RootState } from '@react-three/fiber';
import { OrbitControls, Environment, useFBX } from '@react-three/drei';
import * as THREE from 'three';

interface Character3DProps {
  isAnimating?: boolean;
  animationType?: 'idle' | 'speaking' | 'listening' | 'hello' | 'happy' | 'clapping' | 'curious' | 'dash' | 'frustrated' | 'again';
  modelConfig?: {
    scale?: number;
    position?: [number, number, number];
    rotation?: [number, number, number];
  };
}

// Import the custom hooks
import { useCharacterModel } from '../hooks/useCharacterModel';
import ErrorBoundary from './ErrorBoundary';

// Import validation (will auto-run in development)
import '../utils/validateLABot';
import { DebugTest } from './DebugTest';
import AnimationControls from './AnimationControls';
import { LABOT_ANIMATION_FILES } from '@/utils/modelLoader';

// Fallback procedural character component
function FallbackCharacter({ isAnimating, animationType }: Character3DProps) {
  const meshRef = useRef<THREE.Group>(null);
  const headRef = useRef<THREE.Group>(null);

  useFrame((state: RootState) => {
    if (!meshRef.current) return;

    // Base idle animation
    meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;

    if (headRef.current && animationType === 'speaking') {
      // Head nodding animation when speaking
      headRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 3) * 0.2;
    } else if (headRef.current && animationType === 'listening') {
      // Slight head tilt when listening
      headRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <group ref={meshRef}>
      {/* Body */}
      <mesh position={[0, -1, 0]}>
        <cylinderGeometry args={[1, 1.5, 3, 8]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>

      {/* Head */}
      <group ref={headRef} position={[0, 1.5, 0]}>
        <mesh>
          <sphereGeometry args={[1, 16, 16]} />
          <meshStandardMaterial color="#fbbf24" />
        </mesh>

        {/* Eyes */}
        <mesh position={[-0.3, 0.2, 0.8]}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshStandardMaterial color="#000" />
        </mesh>
        <mesh position={[0.3, 0.2, 0.8]}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshStandardMaterial color="#000" />
        </mesh>

        {/* Mouth */}
        <mesh position={[0, -0.3, 0.8]} rotation={[0, 0, animationType === 'speaking' ? Math.PI / 12 : 0]}>
          <ringGeometry args={[0.1, 0.2, 8]} />
          <meshStandardMaterial color="#000" />
        </mesh>
      </group>

      {/* Arms */}
      <mesh position={[-1.2, 0, 0]} rotation={[0, 0, isAnimating ? Math.sin(Date.now() * 0.01) * 0.3 : 0]}>
        <cylinderGeometry args={[0.3, 0.3, 2, 8]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>
      <mesh position={[1.2, 0, 0]} rotation={[0, 0, isAnimating ? -Math.sin(Date.now() * 0.01) * 0.3 : 0]}>
        <cylinderGeometry args={[0.3, 0.3, 2, 8]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>
    </group>
  );
}

// Enhanced character model component
function CharacterModel({ isAnimating, animationType, modelConfig }: Character3DProps) {
  const characterModel = useCharacterModel(modelConfig);
  const clockRef = useRef<THREE.Clock>(new THREE.Clock());

  // Debug: Log character model state changes (only when loading state changes)
  useEffect(() => {
    console.log('🎭 CharacterModel state:', {
      isLoading: characterModel.isLoading,
      hasCustomModel: characterModel.hasCustomModel,
      error: characterModel.error,
      currentAnimation: characterModel.currentAnimation,
      availableAnimationsCount: characterModel.availableAnimations.length
    });
  }, [characterModel.isLoading, characterModel.hasCustomModel, characterModel.error, characterModel.currentAnimation, characterModel.availableAnimations.length]);

  // Show loading state with animated indicator
  if (characterModel.isLoading) {
    return (
      <group>
        <mesh>
          <boxGeometry args={[1, 2, 1]} />
          <meshStandardMaterial color="#cccccc" transparent opacity={0.5} />
        </mesh>
        {/* Animated loading indicator */}
        <mesh position={[0, 3, 0]}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshStandardMaterial color="#4f46e5" />
        </mesh>
        {/* Loading text */}
        <group position={[0, -2, 0]}>
          <mesh>
            <boxGeometry args={[2, 0.5, 0.1]} />
            <meshStandardMaterial color="#f0f0f0" transparent opacity={0.8} />
          </mesh>
        </group>
      </group>
    );
  }

  // Show error state with detailed information
  if (characterModel.error) {
    console.warn('Character model error:', characterModel.error);
    return (
      <group>
        <FallbackCharacter isAnimating={isAnimating} animationType={animationType} />
        {/* Error indicator */}
        <mesh position={[2, 2, 0]}>
          <sphereGeometry args={[0.2, 8, 8]} />
          <meshStandardMaterial color="#ef4444" />
        </mesh>
      </group>
    );
  }

  // Fallback to procedural character if no custom model
  if (!characterModel.hasCustomModel) {
    console.log('🔄 Using fallback character - hasCustomModel:', characterModel.hasCustomModel, 'isLoading:', characterModel.isLoading);
    return <FallbackCharacter isAnimating={isAnimating} animationType={animationType} />;
  }

  console.log('🎯 Rendering custom model - hasCustomModel:', characterModel.hasCustomModel, 'model:', characterModel.model);

  // Render the custom model with error protection
  try {
    return characterModel.model ? <primitive object={characterModel.model} /> : null;
  } catch (renderError) {
    console.error('Error rendering custom model:', renderError);
    return <FallbackCharacter isAnimating={isAnimating} animationType={animationType} />;
  }
}

function CustomMascotModel() {
  const filePath = LABOT_ANIMATION_FILES['clapping'];
  const model = useFBX(filePath);
  const clappingAnimation = model.animations;

  clappingAnimation[0].name = 'Clapping';
}

const Character3D = ({
  isAnimating = false,
  animationType = 'idle',
  modelConfig
}: Character3DProps) => {
  // State for animation control
  const [currentAnimationType, setCurrentAnimationType] = useState(animationType);

  // Create a stable default config to prevent infinite loops
  const defaultConfig = useMemo(() => ({}), []);
  const stableModelConfig = modelConfig || defaultConfig;
  const characterModel = useCharacterModel(stableModelConfig);

  // Handler for animation changes - now uses the dynamic animation system
  const handleAnimationChange = async (animation: string) => {
    try {
      console.log(`🎬 Character3D: Switching to animation: ${animation}`);
      await characterModel.switchToAnimation(animation);
      setCurrentAnimationType(animation as typeof animationType);
    } catch (error) {
      console.error(`❌ Character3D: Failed to switch to animation ${animation}:`, error);
    }
  };

  // Simple effect to update local state when prop changes
  useEffect(() => {
    setCurrentAnimationType(animationType);
  }, [animationType]);
  const fallbackCharacter = (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="text-center">
        <div className="text-6xl mb-4">🤖</div>
        <p className="text-gray-600">Character unavailable</p>
      </div>
    </div>
  );

  return (
    <ErrorBoundary
      fallback={fallbackCharacter}
      onError={(error) => {
        console.error('Character3D Error:', error);
      }}
    >
      <div className="w-full h-full relative">
        {/* Debug component to test file access */}
        <DebugTest />

        {/* Animation Controls */}
        {characterModel.hasCustomModel && characterModel.availableAnimations.length > 0 && (
          <AnimationControls
            currentAnimation={characterModel.currentAnimation}
            availableAnimations={characterModel.availableAnimations}
            onAnimationChange={handleAnimationChange}
            isLoading={characterModel.isLoading}
          />
        )}

        <Canvas
          camera={{ position: [0, 2, 8], fov: 50 }}
          shadows
          gl={{
            antialias: true,
            alpha: false,
            powerPreference: "high-performance"
          }}
          onCreated={({ gl, scene }) => {
            // Configure renderer for better visibility
            gl.useLegacyLights = false;
            gl.outputColorSpace = THREE.SRGBColorSpace;
            gl.toneMapping = THREE.ACESFilmicToneMapping;
            gl.toneMappingExposure = 1.2;
            gl.setClearColor('#f0f0f0', 1); // Light gray background instead of transparent

            // Add fog for depth perception
            scene.fog = new THREE.Fog('#f0f0f0', 10, 50);
          }}
        >
          {/* Enhanced lighting setup */}
          <ambientLight intensity={0.8} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1.2}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={50}
            shadow-camera-left={-10}
            shadow-camera-right={10}
            shadow-camera-top={10}
            shadow-camera-bottom={-10}
          />
          <pointLight position={[-10, -10, -5]} intensity={0.3} />
          <pointLight position={[5, 5, 5]} intensity={0.4} color="#ffffff" />

          {/* Environment for reflections */}
          <hemisphereLight
            args={["#87CEEB", "#362d1a", 0.3]}
          />

          {/* Environment */}
          <Environment preset="studio" />

          {/* Controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={3}
            maxDistance={20}
            target={[0, 1, 0]}
          />

          <ErrorBoundary fallback={<FallbackCharacter isAnimating={isAnimating} animationType={currentAnimationType} />}>
            <CharacterModel
              isAnimating={isAnimating}
              animationType={currentAnimationType}
              modelConfig={modelConfig}
            />
          </ErrorBoundary>

          {/* Ground plane for shadows */}
          <mesh
            rotation={[-Math.PI / 2, 0, 0]}
            position={[0, -3, 0]}
            receiveShadow
          >
            <planeGeometry args={[20, 20]} />
            <meshStandardMaterial
              color="#f0f0f0"
              transparent
              opacity={0.1}
            />
          </mesh>

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={3}
            maxDistance={20}
            target={[0, 1, 0]}
          />
        </Canvas>
      </div>
    </ErrorBoundary>
  );
};

export default Character3D;
