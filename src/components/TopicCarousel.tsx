
import { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Topic {
  id: string;
  title: string;
  description: string;
  image: string;
}

const topics: Topic[] = [
  {
    id: 'general',
    title: 'General Chat',
    description: 'Have a casual conversation about anything',
    image: '/placeholder.svg'
  },
  {
    id: 'education',
    title: 'Education',
    description: 'Learn about various subjects and topics',
    image: '/placeholder.svg'
  },
  {
    id: 'entertainment',
    title: 'Entertainment',
    description: 'Discuss movies, music, and pop culture',
    image: '/placeholder.svg'
  },
  {
    id: 'health',
    title: 'Health & Wellness',
    description: 'Get tips on healthy living and wellness',
    image: '/placeholder.svg'
  },
  {
    id: 'technology',
    title: 'Technology',
    description: 'Explore the latest in tech and innovation',
    image: '/placeholder.svg'
  }
];

interface TopicCarouselProps {
  onTopicSelect: (topic: Topic) => void;
  onStartChat: () => void;
}

const TopicCarousel = ({ onTopicSelect, onStartChat }: TopicCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % topics.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + topics.length) % topics.length);
  };

  const handleTopicClick = (topic: Topic) => {
    setSelectedTopic(topic);
    onTopicSelect(topic);
  };

  const getVisibleTopics = () => {
    const visible = [];
    for (let i = 0; i < 3; i++) {
      const index = (currentIndex + i) % topics.length;
      visible.push(topics[index]);
    }
    return visible;
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-8">
      <div className="mb-8 text-center">
        <h1 className="text-5xl font-bold text-gray-800 mb-4">
          AI Character Chat
        </h1>
        <p className="text-xl text-gray-600">
          Choose a topic to start your conversation
        </p>
      </div>

      <div className="relative w-full max-w-6xl mb-12">
        <div className="flex items-center justify-center space-x-6">
          <Button
            onClick={prevSlide}
            variant="outline"
            size="icon"
            className="h-12 w-12 rounded-full"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>

          <div className="flex space-x-4">
            {getVisibleTopics().map((topic, index) => (
              <div
                key={topic.id}
                className={`bg-white rounded-xl shadow-lg p-6 cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                  index === 1 ? 'scale-110 border-4 border-blue-500' : 'scale-100'
                } ${
                  selectedTopic?.id === topic.id ? 'ring-4 ring-blue-300' : ''
                }`}
                style={{ width: '280px', height: '320px' }}
                onClick={() => handleTopicClick(topic)}
              >
                <div className="h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {topic.title.charAt(0)}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {topic.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {topic.description}
                </p>
              </div>
            ))}
          </div>

          <Button
            onClick={nextSlide}
            variant="outline"
            size="icon"
            className="h-12 w-12 rounded-full"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </div>
      </div>

      <Button
        onClick={onStartChat}
        size="lg"
        className="px-12 py-4 text-xl font-semibold bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-200"
      >
        Start Chat
      </Button>

      {selectedTopic && (
        <div className="mt-6 text-center">
          <p className="text-lg text-gray-700">
            Selected: <span className="font-semibold text-blue-600">{selectedTopic.title}</span>
          </p>
        </div>
      )}
    </div>
  );
};

export default TopicCarousel;
