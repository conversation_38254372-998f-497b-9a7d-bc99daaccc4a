import React, { useEffect, useRef, useState } from 'react';
import { useAnimations, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

interface LABotCharacterProps {
  animationType?: 'idle' | 'clapping' | 'curious' | 'dash' | 'frustrated' | 'happy' | 'hello';
  scale?: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
}

export function LABotCharacter({
  animationType = 'clapping',
  scale = 0.01,
  position = [0, -1, 0],
  rotation = [0, 0, 0]
}: LABotCharacterProps) {
  const group = useRef<THREE.Group>(null);
  const [currentAnimation, setCurrentAnimation] = useState(animationType);

  // Load base model and animations separately (like the working example)
  const baseModel = useFBX('/LABot/LABot.fbx'); // Base model without animations
  const { animations: clappingAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: idleAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: curiousAnimations } = useFBX('/LABot/<EMAIL>');

  // Prepare animations like the working example
  const allAnimations = React.useMemo(() => {
    const animations: THREE.AnimationClip[] = [];

    // Name animations explicitly like the working example
    if (clappingAnimations[0]) {
      clappingAnimations[0].name = 'clapping';
      animations.push(clappingAnimations[0]);
    }
    if (idleAnimations[0]) {
      idleAnimations[0].name = 'idle';
      animations.push(idleAnimations[0]);
    }
    if (curiousAnimations[0]) {
      curiousAnimations[0].name = 'curious';
      animations.push(curiousAnimations[0]);
    }

    console.log('🎬 Prepared animations:', animations.map(a => ({ name: a.name, duration: a.duration })));
    return animations;
  }, [clappingAnimations, idleAnimations, curiousAnimations]);

  // Use drei's useAnimations hook exactly like the working example
  const { actions } = useAnimations(allAnimations, group);

  // Apply textures and load the base model (only once)
  useEffect(() => {
    if (baseModel && group.current) {
      console.log('🎨 Loading LABot base model with textures');

      // Clone the model to avoid modifying the original
      const clonedModel = baseModel.clone();

      // Clear existing children and add the cloned model
      group.current.clear();
      group.current.add(clonedModel);

      // Apply base color texture
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load('/LABot/Texture/LABot_Base_Color.jpg',
        (texture) => {
          console.log('✅ Base color texture loaded');
          texture.flipY = false;
          texture.colorSpace = THREE.SRGBColorSpace;

          // Apply texture to all meshes
          clonedModel.traverse((child: THREE.Object3D) => {
            if (child instanceof THREE.Mesh) {
              const material = new THREE.MeshStandardMaterial({
                map: texture,
                roughness: 0.7,
                metalness: 0.1,
              });
              child.material = material;
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
        }
      );
    }
  }, [baseModel]); // Only depend on baseModel, not animation changes

  // Handle animation changes - exactly like the working example
  useEffect(() => {
    console.log(`🎬 Animation changed to: ${currentAnimation}`);
    console.log('Available actions:', Object.keys(actions));

    if (actions[currentAnimation]) {
      console.log(`🎬 Playing animation: ${currentAnimation}`);

      // Simple approach like the working example
      actions[currentAnimation].reset().fadeIn(0.5).play();

      // Return cleanup function like the working example
      return () => {
        actions[currentAnimation].fadeOut(0.5);
      };
    } else {
      console.warn(`❌ Animation not found: ${currentAnimation}`);
      console.log('Available actions:', Object.keys(actions));
    }
  }, [currentAnimation, actions]);

  // Update animation when prop changes
  useEffect(() => {
    setCurrentAnimation(animationType);
  }, [animationType]);

  return (
    <group
      ref={group}
      scale={[scale, scale, scale]}
      position={position}
      rotation={rotation}
      dispose={null}
    />
  );
}

// Preload the main model
useFBX.preload('/LABot/<EMAIL>');
