import React, { useEffect, useRef, useState } from 'react';
import { useAnimations, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

interface LABotCharacterProps {
  animationType?: 'idle' | 'clapping' | 'curious' | 'dash' | 'frustrated' | 'happy' | 'hello';
  scale?: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
}

export function LABotCharacter({
  animationType = 'clapping',
  scale = 0.01,
  position = [0, -1, 0],
  rotation = [0, 0, 0]
}: LABotCharacterProps) {
  const group = useRef<THREE.Group>(null);
  const [currentAnimation, setCurrentAnimation] = useState(animationType);

  // Load the main model and all animations from their respective FBX files
  const { scene: clappingModel, animations: clappingAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: idleAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: curiousAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: dashAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: frustratedAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: happyAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: helloAnimations } = useFBX('/LABot/<EMAIL>');

  // Prepare animations with proper names
  const allAnimations = React.useMemo(() => {
    const animations: THREE.AnimationClip[] = [];

    if (idleAnimations[0]) {
      idleAnimations[0].name = 'idle';
      animations.push(idleAnimations[0]);
    }
    if (clappingAnimations[0]) {
      clappingAnimations[0].name = 'clapping';
      animations.push(clappingAnimations[0]);
    }
    if (curiousAnimations[0]) {
      curiousAnimations[0].name = 'curious';
      animations.push(curiousAnimations[0]);
    }
    if (dashAnimations[0]) {
      dashAnimations[0].name = 'dash';
      animations.push(dashAnimations[0]);
    }
    if (frustratedAnimations[0]) {
      frustratedAnimations[0].name = 'frustrated';
      animations.push(frustratedAnimations[0]);
    }
    if (happyAnimations[0]) {
      happyAnimations[0].name = 'happy';
      animations.push(happyAnimations[0]);
    }
    if (helloAnimations[0]) {
      helloAnimations[0].name = 'hello';
      animations.push(helloAnimations[0]);
    }

    console.log('🎬 Prepared animations:', animations.map(a => ({ name: a.name, duration: a.duration })));
    return animations;
  }, [idleAnimations, clappingAnimations, curiousAnimations, dashAnimations, frustratedAnimations, happyAnimations, helloAnimations]);

  // Use drei's useAnimations hook - this handles the mixer automatically
  const { actions } = useAnimations(allAnimations, group);

  // Apply textures to the model
  useEffect(() => {
    if (clappingModel && group.current) {
      console.log('🎨 Applying textures to LABot model');

      // Clone the model to avoid modifying the original
      const clonedModel = clappingModel.clone();

      // Clear existing children and add the cloned model
      group.current.clear();
      group.current.add(clonedModel);

      // Apply base color texture
      const textureLoader = new THREE.TextureLoader();
      const baseColorTexture = textureLoader.load('/LABot/Texture/LABot_Base_Color.jpg',
        (texture) => {
          console.log('✅ Base color texture loaded');
          texture.flipY = false;
          texture.colorSpace = THREE.SRGBColorSpace;

          // Apply texture to all meshes
          clonedModel.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              const material = new THREE.MeshStandardMaterial({
                map: texture,
                roughness: 0.7,
                metalness: 0.1,
              });
              child.material = material;
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
        }
      );
    }
  }, [clappingModel]);

  // Handle animation changes
  useEffect(() => {
    if (actions && actions[currentAnimation]) {
      console.log(`🎬 Playing animation: ${currentAnimation}`);

      // Stop all other animations
      Object.values(actions).forEach(action => {
        if (action) action.stop();
      });

      // Play the current animation
      const action = actions[currentAnimation];
      if (action) {
        action.reset().fadeIn(0.5).play();
        action.setLoop(THREE.LoopRepeat, Infinity);
        console.log(`✅ Animation ${currentAnimation} started`);
      }
    }
  }, [actions, currentAnimation]);

  // Update animation when prop changes
  useEffect(() => {
    setCurrentAnimation(animationType);
  }, [animationType]);

  return (
    <group
      ref={group}
      scale={[scale, scale, scale]}
      position={position}
      rotation={rotation}
      dispose={null}
    />
  );
}

// Preload the main model
useFBX.preload('/LABot/<EMAIL>');
