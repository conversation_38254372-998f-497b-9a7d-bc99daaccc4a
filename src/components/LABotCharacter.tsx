import React, { useEffect, useRef, useState } from 'react';
import { useAnimations, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

interface LABotCharacterProps {
  animationType?: 'idle' | 'clapping' | 'curious' | 'dash' | 'frustrated' | 'happy' | 'hello';
  scale?: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
}

export function LABotCharacter({
  animationType = 'clapping',
  scale = 0.01,
  position = [0, -1, 0],
  rotation = [0, 0, 0]
}: LABotCharacterProps) {
  const group = useRef<THREE.Group>(null);
  const [currentAnimation, setCurrentAnimation] = useState(animationType);

  // Load the base model (use clapping as base since it has good geometry)
  const baseModel = useFBX('/LABot/<EMAIL>');

  // Load all animations from their respective FBX files
  const { animations: idleAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: clappingAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: curiousAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: dashAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: frustratedAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: happyAnimations } = useFBX('/LABot/<EMAIL>');
  const { animations: helloAnimations } = useFBX('/LABot/<EMAIL>');

  // Prepare all animations with proper names
  const allAnimations = React.useMemo(() => {
    const animations: THREE.AnimationClip[] = [];

    if (idleAnimations[0]) {
      const anim = idleAnimations[0].clone();
      anim.name = 'idle';
      animations.push(anim);
    }
    if (clappingAnimations[0]) {
      const anim = clappingAnimations[0].clone();
      anim.name = 'clapping';
      animations.push(anim);
    }
    if (curiousAnimations[0]) {
      const anim = curiousAnimations[0].clone();
      anim.name = 'curious';
      animations.push(anim);
    }
    if (dashAnimations[0]) {
      const anim = dashAnimations[0].clone();
      anim.name = 'dash';
      animations.push(anim);
    }
    if (frustratedAnimations[0]) {
      const anim = frustratedAnimations[0].clone();
      anim.name = 'frustrated';
      animations.push(anim);
    }
    if (happyAnimations[0]) {
      const anim = happyAnimations[0].clone();
      anim.name = 'happy';
      animations.push(anim);
    }
    if (helloAnimations[0]) {
      const anim = helloAnimations[0].clone();
      anim.name = 'hello';
      animations.push(anim);
    }

    console.log('🎬 Prepared animations:', animations.map(a => ({ name: a.name, duration: a.duration })));
    return animations;
  }, [idleAnimations, clappingAnimations, curiousAnimations, dashAnimations, frustratedAnimations, happyAnimations, helloAnimations]);

  // Use drei's useAnimations hook with all animations
  const { actions } = useAnimations(allAnimations, group);

  // Apply textures and load the base model (only once)
  useEffect(() => {
    if (baseModel && group.current) {
      console.log('🎨 Loading LABot base model with textures');

      // Clone the model to avoid modifying the original
      const clonedModel = baseModel.clone();

      // Clear existing children and add the cloned model
      group.current.clear();
      group.current.add(clonedModel);

      // Apply base color texture
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load('/LABot/Texture/LABot_Base_Color.jpg',
        (texture) => {
          console.log('✅ Base color texture loaded');
          texture.flipY = false;
          texture.colorSpace = THREE.SRGBColorSpace;

          // Apply texture to all meshes
          clonedModel.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              const material = new THREE.MeshStandardMaterial({
                map: texture,
                roughness: 0.7,
                metalness: 0.1,
              });
              child.material = material;
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
        }
      );
    }
  }, [baseModel]); // Only depend on baseModel, not animation changes

  // Handle animation changes
  useEffect(() => {
    console.log(`🎬 Animation changed to: ${currentAnimation}`);
    console.log('Available actions:', Object.keys(actions));
    console.log('Prepared animations:', preparedAnimation.map(a => a.name));

    if (actions && actions[currentAnimation]) {
      console.log(`🎬 Playing animation: ${currentAnimation}`);

      // Stop all other animations
      Object.values(actions).forEach(action => {
        if (action) action.stop();
      });

      // Play the current animation
      const action = actions[currentAnimation];
      if (action) {
        action.reset().fadeIn(0.5).play();
        action.setLoop(THREE.LoopRepeat, Infinity);
        console.log(`✅ Animation ${currentAnimation} started`);
      }
    } else {
      console.warn(`❌ Animation not found: ${currentAnimation}`);
      console.log('Current model data:', currentModelData);
    }
  }, [actions, currentAnimation, preparedAnimation, currentModelData]);

  // Update animation when prop changes
  useEffect(() => {
    setCurrentAnimation(animationType);
  }, [animationType]);

  return (
    <group
      ref={group}
      scale={[scale, scale, scale]}
      position={position}
      rotation={rotation}
      dispose={null}
    />
  );
}

// Preload the main model
useFBX.preload('/LABot/<EMAIL>');
