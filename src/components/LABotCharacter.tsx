import React, { useEffect, useRef, useState } from 'react';
import { useAnimations, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

interface LABotCharacterProps {
  animationType?: 'idle' | 'clapping' | 'curious' | 'dash' | 'frustrated' | 'happy' | 'hello';
  scale?: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
}

export function LABotCharacter({
  animationType = 'clapping',
  scale = 0.01,
  position = [0, -1, 0],
  rotation = [0, 0, 0]
}: LABotCharacterProps) {
  const group = useRef<THREE.Group>(null);
  const [currentAnimation, setCurrentAnimation] = useState(animationType);

  // Load each complete animated model (like the working example)
  const clappingFBX = useFBX('/LABot/<EMAIL>');
  const idleFBX = useFBX('/LABot/<EMAIL>');
  const curiousFBX = useFBX('/LABot/<EMAIL>');
  const dashFBX = useFBX('/LABot/<EMAIL>');
  const frustratedFBX = useFBX('/LABot/<EMAIL>');
  const happyFBX = useFBX('/LABot/<EMAIL>');
  const helloFBX = useFBX('/LABot/<EMAIL>');

  // Get the current model and animation based on animationType
  const currentModelData = React.useMemo(() => {
    switch (currentAnimation) {
      case 'idle':
        return { model: idleFBX, animations: idleFBX.animations };
      case 'clapping':
        return { model: clappingFBX, animations: clappingFBX.animations };
      case 'curious':
        return { model: curiousFBX, animations: curiousFBX.animations };
      case 'dash':
        return { model: dashFBX, animations: dashFBX.animations };
      case 'frustrated':
        return { model: frustratedFBX, animations: frustratedFBX.animations };
      case 'happy':
        return { model: happyFBX, animations: happyFBX.animations };
      case 'hello':
        return { model: helloFBX, animations: helloFBX.animations };
      default:
        return { model: clappingFBX, animations: clappingFBX.animations };
    }
  }, [currentAnimation, clappingFBX, idleFBX, curiousFBX, dashFBX, frustratedFBX, happyFBX, helloFBX]);

  // Prepare the animation for useAnimations
  const preparedAnimation = React.useMemo(() => {
    if (currentModelData.animations && currentModelData.animations[0]) {
      const animation = currentModelData.animations[0].clone();
      animation.name = currentAnimation;
      console.log(`🎬 Prepared animation: ${currentAnimation}`, { duration: animation.duration });
      return [animation];
    }
    return [];
  }, [currentModelData.animations, currentAnimation]);

  // Use drei's useAnimations hook with the current animation
  const { actions } = useAnimations(preparedAnimation, group);

  // Apply textures and load the current model
  useEffect(() => {
    if (currentModelData.model && group.current) {
      console.log(`🎨 Loading model for animation: ${currentAnimation}`);

      // Clone the model to avoid modifying the original
      const clonedModel = currentModelData.model.clone();

      // Clear existing children and add the cloned model
      group.current.clear();
      group.current.add(clonedModel);

      // Apply base color texture
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load('/LABot/Texture/LABot_Base_Color.jpg',
        (texture) => {
          console.log('✅ Base color texture loaded');
          texture.flipY = false;
          texture.colorSpace = THREE.SRGBColorSpace;

          // Apply texture to all meshes
          clonedModel.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              const material = new THREE.MeshStandardMaterial({
                map: texture,
                roughness: 0.7,
                metalness: 0.1,
              });
              child.material = material;
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
        }
      );
    }
  }, [currentModelData.model, currentAnimation]);

  // Handle animation changes
  useEffect(() => {
    if (actions && actions[currentAnimation]) {
      console.log(`🎬 Playing animation: ${currentAnimation}`);

      // Stop all other animations
      Object.values(actions).forEach(action => {
        if (action) action.stop();
      });

      // Play the current animation
      const action = actions[currentAnimation];
      if (action) {
        action.reset().fadeIn(0.5).play();
        action.setLoop(THREE.LoopRepeat, Infinity);
        console.log(`✅ Animation ${currentAnimation} started`);
      }
    }
  }, [actions, currentAnimation]);

  // Update animation when prop changes
  useEffect(() => {
    setCurrentAnimation(animationType);
  }, [animationType]);

  return (
    <group
      ref={group}
      scale={[scale, scale, scale]}
      position={position}
      rotation={rotation}
      dispose={null}
    />
  );
}

// Preload the main model
useFBX.preload('/LABot/<EMAIL>');
