# LABot 3D Model Assets

This directory contains the custom 3D character model files for the LABot character.

## Current Directory Structure

```
LABot/
├── README.md                    # This file
├── LABot.fbx                   # Main 3D model file (FBX format)
├── LABot_rig.blend             # Blender rig file
├── <EMAIL>             # Again animation
├── <EMAIL>          # Clapping animation
├── <EMAIL>           # Curious animation
├── <EMAIL>              # Dash animation
├── <EMAIL>        # Frustrated animation
├── <EMAIL>             # Happy animation
├── <EMAIL>             # Hello animation
├── <EMAIL>              # Idle animation
├── Texture/                    # Texture files directory
│   ├── LABot_Base_Color.jpg    # Main color texture
│   ├── LABot_Normal.png        # Normal map texture
│   ├── LABot_Roughness.jpg     # Roughness texture
│   ├── LABot_Metallic.jpg      # Metallic texture
│   ├── LABot_Mixed_AO.jpg      # Ambient occlusion texture
│   ├── LABot_Height.jpg        # Height/displacement map
│   ├── eye_Base_Color.png      # Eye texture
│   └── Eyes/                   # Animated eye textures
│       ├── Again/              # Eye textures for Again animation
│       ├── Frustrated/         # Eye textures for Frustrated animation
│       └── Hello/              # Eye textures for Hello animation
└── render1.png                 # Preview render
```

## Supported File Formats

- **Models**: .fbx (primary), .gltf, .glb
- **Textures**: .jpg, .jpeg, .png, .webp
- **Animations**: Separate .fbx files with embedded animations

## Animation States

The system supports 8 animation states that are automatically loaded:
1. **idle** - Default resting animation (<EMAIL>)
2. **hello** - Greeting gesture (<EMAIL>)
3. **happy** - Positive emotion expression (<EMAIL>)
4. **clapping** - Applause animation (<EMAIL>)
5. **curious** - Inquisitive pose/movement (<EMAIL>)
6. **dash** - Quick movement animation (<EMAIL>)
7. **frustrated** - Negative emotion expression (<EMAIL>)
8. **again** - Repeat/retry gesture (<EMAIL>)

## Texture Mapping

The system automatically detects and applies these textures:
- **LABot_Base_Color.jpg** - Main diffuse/albedo texture
- **LABot_Normal.png** - Normal map for surface details
- **LABot_Roughness.jpg** - Surface roughness map
- **LABot_Metallic.jpg** - Metallic surface map
- **LABot_Mixed_AO.jpg** - Ambient occlusion for depth
- **LABot_Height.jpg** - Height/displacement mapping

## Eye Animation System

The Eyes/ directory contains animated eye textures for different emotional states:
- **Again/**: Eye expressions for retry/repeat actions
- **Frustrated/**: Eye expressions for negative emotions
- **Hello/**: Eye expressions for greetings

Each subdirectory contains numbered PNG files (01-10) for frame-by-frame eye animation.

## Usage

The Character3D component automatically:
1. Loads the main LABot.fbx model
2. Applies all available textures
3. Loads all 8 animation files
4. Sets up the animation system with smooth transitions
5. Handles intelligent animation selection based on conversation context

## Technical Details

- **Model Scale**: Automatically scaled to 0.01 (FBX models are typically large)
- **Position**: Positioned at [0, -1, 0] for proper ground alignment
- **Shadows**: Enabled for realistic lighting
- **Materials**: Enhanced with PBR (Physically Based Rendering) textures
- **Performance**: Optimized loading with error handling and fallbacks

## Troubleshooting

If the model doesn't load properly:
1. Check the browser console for error messages
2. Ensure all FBX files are valid and not corrupted
3. Verify texture file formats are supported
4. Test with a simplified model first

The system includes comprehensive error handling and will fall back to a procedural character if needed.

## Animation Context Mapping

The system intelligently selects animations based on conversation context:
- **Speaking "hello"** → hello animation
- **Positive words** → happy animation
- **Questions** → curious animation
- **Problems/errors** → frustrated animation
- **Congratulations** → clapping animation
- **Quick actions** → dash animation
- **Repetition requests** → again animation
- **Default/idle** → idle animation
